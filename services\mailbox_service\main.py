"""
Mailbox Connection Microservice
Handles email connection, filtering, and ZIP attachment downloading
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.responses import JSONResponse
import uvicorn
import os
from typing import List, Optional
from sqlalchemy.orm import Session

from shared.schemas.mailbox import EmailProcessRequest, EmailProcessResponse, EmailInfo, EmailListResponse
from shared.database.connection import get_db, create_tables
from shared.database.models import EmailRecord
from shared.utils.logger import get_logger
from email_service import EmailService

# Initialize FastAPI app
app = FastAPI(
    title="Mailbox Service",
    description="Microservice for connecting to mailboxes and downloading ZIP attachments",
    version="1.0.0"
)

logger = get_logger(__name__)

# Initialize email service
email_service = EmailService()

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    """Create database tables on startup"""
    try:
        create_tables()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "mailbox-service"}

# Email processing endpoints
@app.post("/process-emails", response_model=EmailProcessResponse)
async def process_emails(
    request: EmailProcessRequest,
    db: Session = Depends(get_db)
):
    """
    Connect to mailbox and process emails with optimized ZIP attachment filtering
    """
    try:
        logger.info(f"Processing emails for mailbox: {request.email_host} (optimized batch processing)")

        # Process emails using the optimized email service
        processed_count, downloaded_files, errors = email_service.process_emails(request, db)

        success = len(errors) == 0
        message = f"Processed {processed_count} emails"
        if downloaded_files:
            message += f", downloaded {len(downloaded_files)} ZIP files"
        if errors:
            message += f" with {len(errors)} errors"

        return EmailProcessResponse(
            success=success,
            message=message,
            processed_count=processed_count,
            downloaded_files=downloaded_files,
            errors=errors if errors else None
        )

    except Exception as e:
        logger.error(f"Error processing emails: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Email processing failed: {str(e)}")

@app.post("/process-emails-batch", response_model=EmailProcessResponse)
async def process_emails_batch(
    request: EmailProcessRequest,
    batch_size: int = 50,
    zip_only: bool = True,
    db: Session = Depends(get_db)
):
    """
    Connect to mailbox and process emails with configurable batch processing

    Args:
        request: Email processing request
        batch_size: Number of emails to process in each batch (default: 50)
        zip_only: If True, only process emails with ZIP attachments (default: True)
        db: Database session
    """
    try:
        logger.info(f"Batch processing emails for mailbox: {request.email_host} (batch_size: {batch_size}, zip_only: {zip_only})")

        # Connect to mailbox
        mail = email_service.connect_to_mailbox(request)

        try:
            # Search for all emails first
            all_email_ids = email_service.search_emails_with_attachments(mail, request)

            if not all_email_ids:
                return EmailProcessResponse(
                    success=True,
                    message="No emails found matching criteria",
                    processed_count=0,
                    downloaded_files=[],
                    errors=None
                )

            processed_count = 0
            downloaded_files = []
            errors = []

            if zip_only:
                # Filter for ZIP attachments first
                logger.info("Filtering emails for ZIP attachments...")
                zip_email_ids = email_service.batch_filter_emails_with_zip_attachments(mail, all_email_ids, batch_size)

                if not zip_email_ids:
                    return EmailProcessResponse(
                        success=True,
                        message=f"No emails with ZIP attachments found out of {len(all_email_ids)} total emails",
                        processed_count=0,
                        downloaded_files=[],
                        errors=None
                    )

                # Process ZIP emails in batches
                for i in range(0, len(zip_email_ids), batch_size):
                    batch = zip_email_ids[i:i + batch_size]
                    batch_num = (i // batch_size) + 1
                    total_batches = (len(zip_email_ids) + batch_size - 1) // batch_size

                    logger.info(f"Processing ZIP email batch {batch_num}/{total_batches} ({len(batch)} emails)")

                    for email_id in batch:
                        try:
                            email_info = email_service.get_email_info(mail, email_id)
                            email_record = email_service.store_email_record(email_info, db, request.client_id)

                            if email_info['has_zip_attachment']:
                                email_downloaded_files = email_service.download_zip_attachments(email_info, db)
                                downloaded_files.extend(email_downloaded_files)
                                email_service.store_zip_file_records(email_downloaded_files, email_record, db)

                            email_record.processing_status = "completed"
                            db.commit()
                            processed_count += 1

                        except Exception as e:
                            error_msg = f"Failed to process email {email_id.decode()}: {str(e)}"
                            logger.error(error_msg)
                            errors.append(error_msg)
            else:
                # Process all emails in batches
                for i in range(0, len(all_email_ids), batch_size):
                    batch = all_email_ids[i:i + batch_size]
                    batch_num = (i // batch_size) + 1
                    total_batches = (len(all_email_ids) + batch_size - 1) // batch_size

                    logger.info(f"Processing email batch {batch_num}/{total_batches} ({len(batch)} emails)")

                    for email_id in batch:
                        try:
                            email_info = email_service.get_email_info(mail, email_id)
                            email_record = email_service.store_email_record(email_info, db, request.client_id)

                            if email_info['has_zip_attachment']:
                                email_downloaded_files = email_service.download_zip_attachments(email_info, db)
                                downloaded_files.extend(email_downloaded_files)
                                email_service.store_zip_file_records(email_downloaded_files, email_record, db)

                            email_record.processing_status = "completed"
                            db.commit()
                            processed_count += 1

                        except Exception as e:
                            error_msg = f"Failed to process email {email_id.decode()}: {str(e)}"
                            logger.error(error_msg)
                            errors.append(error_msg)

            success = len(errors) == 0
            message = f"Batch processed {processed_count} emails"
            if downloaded_files:
                message += f", downloaded {len(downloaded_files)} ZIP files"
            if errors:
                message += f" with {len(errors)} errors"

            return EmailProcessResponse(
                success=success,
                message=message,
                processed_count=processed_count,
                downloaded_files=downloaded_files,
                errors=errors if errors else None
            )

        finally:
            try:
                mail.close()
                mail.logout()
            except:
                pass

    except Exception as e:
        logger.error(f"Error in batch processing emails: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch email processing failed: {str(e)}")

@app.get("/emails", response_model=EmailListResponse)
async def list_emails(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    List processed emails
    """
    try:
        # Query emails with pagination
        emails_query = db.query(EmailRecord).offset(skip).limit(limit)
        email_records = emails_query.all()

        # Get total count
        total_count = db.query(EmailRecord).count()

        # Convert to response format
        emails = []
        for record in email_records:
            email_info = EmailInfo(
                email_id=record.email_id,
                sender=record.sender,
                subject=record.subject or "",
                reception_date=record.reception_date,
                has_zip_attachment=record.has_zip_attachment,
                attachment_count=0  # Could be enhanced to store this
            )
            emails.append(email_info)

        return EmailListResponse(
            emails=emails,
            total_count=total_count,
            has_more=(skip + limit) < total_count
        )

    except Exception as e:
        logger.error(f"Error listing emails: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list emails: {str(e)}")

@app.get("/status")
async def get_processing_status(db: Session = Depends(get_db)):
    """Get current processing status"""
    try:
        # Get statistics from database
        total_emails = db.query(EmailRecord).count()
        processed_emails = db.query(EmailRecord).filter(
            EmailRecord.processing_status == "completed"
        ).count()
        failed_emails = db.query(EmailRecord).filter(
            EmailRecord.processing_status == "failed"
        ).count()

        # Get last processed email
        last_processed_email = db.query(EmailRecord).filter(
            EmailRecord.processing_status == "completed"
        ).order_by(EmailRecord.processed_date.desc()).first()

        last_processed = None
        if last_processed_email:
            last_processed = last_processed_email.processed_date.isoformat()

        return {
            "service": "mailbox-service",
            "status": "running",
            "total_emails": total_emails,
            "processed_emails": processed_emails,
            "failed_emails": failed_emails,
            "last_processed": last_processed
        }

    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return {
            "service": "mailbox-service",
            "status": "error",
            "error": str(e)
        }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8001)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
