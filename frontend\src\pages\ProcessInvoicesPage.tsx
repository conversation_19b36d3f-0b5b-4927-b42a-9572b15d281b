import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import FileUpload from '../components/FileUpload'
import EmailProcessor from '../components/EmailProcessor'
import AdminLayout from '../components/AdminLayout'
import UserLayout from '../components/UserLayout'

const ProcessInvoicesPage: React.FC = () => {
  const { user, client, logout } = useAuth()
  const [activeTab, setActiveTab] = useState<'upload' | 'email'>('upload')
  const [results, setResults] = useState<any[]>([])
  const [error, setError] = useState<string>('')
  const [success, setSuccess] = useState<string>('')

  const handleSuccess = (result: any) => {
    setResults(prev => [...prev, result])
    setError('')
    setSuccess('Processing completed successfully! Check your dashboard for results.')
    
    // Clear success message after 5 seconds
    setTimeout(() => setSuccess(''), 5000)
  }

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
    setSuccess('')
  }

  const clearMessages = () => {
    setError('')
    setSuccess('')
  }

  const LayoutComponent = user?.is_admin ? AdminLayout : UserLayout

  return (
    <LayoutComponent>
      {/* Main Content */}
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          
          {/* Tab Navigation */}
          <div className="mb-8">
            <nav className="flex space-x-8">
              <button
                onClick={() => {
                  setActiveTab('upload')
                  clearMessages()
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'upload'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                📁 Upload Files
              </button>
              <button
                onClick={() => {
                  setActiveTab('email')
                  clearMessages()
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'email'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                📧 Process Emails
              </button>
            </nav>
          </div>

          {/* Messages */}
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
                <div className="ml-auto pl-3">
                  <button
                    onClick={() => setError('')}
                    className="text-red-400 hover:text-red-600"
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-800">{success}</p>
                </div>
                <div className="ml-auto pl-3">
                  <button
                    onClick={() => setSuccess('')}
                    className="text-green-400 hover:text-green-600"
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Tab Content */}
          <div className="card">
            {activeTab === 'upload' ? (
              <div>
                <div className="mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">Upload Invoice Files</h2>
                  <p className="text-gray-600">
                    Upload ZIP files containing Colombian electronic invoices in XML format. 
                    The system will automatically extract CUFE codes and process the invoices.
                  </p>
                </div>
                <FileUpload
                  onUploadSuccess={handleSuccess}
                  onUploadError={handleError}
                />
              </div>
            ) : (
              <div>
                <div className="mb-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">Process Email Invoices</h2>
                  <p className="text-gray-600">
                    Connect to your email account to automatically download and process 
                    invoice attachments. The system will search for ZIP files containing invoices.
                  </p>
                </div>
                <EmailProcessor
                  onProcessSuccess={handleSuccess}
                  onProcessError={handleError}
                />
              </div>
            )}
          </div>

          {/* Processing Results */}
          {results.length > 0 && (
            <div className="mt-8 card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Processing Results</h3>
              <div className="space-y-3">
                {results.slice(-5).map((result, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">
                          {result.filename || 'Email Processing'}
                        </p>
                        <p className="text-sm text-gray-600">
                          {result.message || result.uploadResult?.message || 'Processed successfully'}
                        </p>
                      </div>
                      <div className="text-green-600">
                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

        </div>
      </div>
    </LayoutComponent>
  )
}

export default ProcessInvoicesPage
