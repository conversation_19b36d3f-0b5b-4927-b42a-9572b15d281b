"""
Pydantic schemas for mailbox service
"""

from pydantic import BaseModel, EmailStr, validator
from typing import List, Optional
from datetime import datetime

class EmailProcessRequest(BaseModel):
    """
    Request schema for email processing with date-based filtering
    """
    email_host: str
    email_port: int = 993
    email_username: EmailStr
    email_password: str
    use_ssl: bool = True
    folder: str = "INBOX"

    # Date filtering (primary filtering method)
    date_filter: Optional[str] = None  # Format: "SINCE 01-Jan-2024" or "SINCE 01-Jan-2024 BEFORE 31-Jan-2024"
    start_date: Optional[datetime] = None  # Alternative: start date for filtering
    end_date: Optional[datetime] = None    # Alternative: end date for filtering

    # Legacy parameter (deprecated in favor of date filtering)
    max_emails: Optional[int] = None  # Deprecated: Use date filtering instead

    client_id: Optional[int] = None  # Client ID for associating processed emails

    @validator('date_filter', pre=True, always=True)
    def build_date_filter_from_dates(cls, v, values):
        """
        Build date_filter from start_date and end_date if date_filter is not provided
        """
        if v is not None:
            return v

        start_date = values.get('start_date')
        end_date = values.get('end_date')

        if start_date or end_date:
            from services.mailbox_service.email_service import EmailService
            return EmailService.create_date_range_filter(start_date, end_date)

        return v

    @validator('max_emails')
    def warn_deprecated_max_emails(cls, v):
        """
        Warn about deprecated max_emails parameter
        """
        if v is not None:
            import warnings
            warnings.warn(
                "max_emails parameter is deprecated. Use date_filter, start_date, or end_date instead.",
                DeprecationWarning,
                stacklevel=2
            )
        return v

class DownloadedFile(BaseModel):
    """
    Schema for downloaded file information
    """
    filename: str
    file_path: str
    file_size: int
    email_id: str
    download_date: datetime

class EmailProcessResponse(BaseModel):
    """
    Response schema for email processing
    """
    success: bool
    message: str
    processed_count: int
    downloaded_files: List[DownloadedFile]
    errors: Optional[List[str]] = None

class EmailInfo(BaseModel):
    """
    Schema for email information
    """
    email_id: str
    sender: str
    subject: str
    reception_date: datetime
    has_zip_attachment: bool
    attachment_count: int

class EmailListResponse(BaseModel):
    """
    Response schema for listing emails
    """
    emails: List[EmailInfo]
    total_count: int
    has_more: bool
