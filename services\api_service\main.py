"""
Main FastAPI REST API Service
Orchestrates all microservices and provides public API endpoints
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, status
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import httpx
from typing import List, Optional
from urllib.parse import unquote
from sqlalchemy.orm import Session
from datetime import datetime
import io
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

from shared.schemas.api import ProcessEmailsRequest, CUFEResponse, CUFEListResponse
from shared.schemas.mailbox import EmailProcessRequest, EmailProcessResponse
from shared.schemas.auth import LoginRequest, LoginResponse, UserCreate, ClientCreate
from shared.schemas.admin import (
    AdminUser<PERSON><PERSON>, AdminUserUpdate, AdminUserResponse, UserListResponse,
    UserSuspendRequest, AdminDashboardStats, PaginationParams, AdminResponse,
    LLMUsageResponse, DeploymentCostResponse, LLMCostSummary, DeploymentCostSummary,
    LLMUsageFilter, DeploymentCostFilter
)
from shared.schemas.extraction import InvoiceLineItemData, InvoiceAllowanceChargeData, InvoicePaymentTermData, MeasurementUnit
from shared.database.connection import get_db, create_tables
from shared.database.models import (
    CUFERecord, EmailRecord, User, Client, InvoiceLineItem, InvoiceAllowanceCharge,
    InvoicePaymentTerm, LLMUsageRecord, DeploymentCostRecord
)
from shared.utils.logger import get_logger
from shared.utils.auth import (
    verify_token, create_user_token, authenticate_user,
    get_current_user, get_current_client, get_current_admin_user, security
)
from shared.utils.password import hash_password

# Initialize FastAPI app
app = FastAPI(
    title="CUFE Extraction API",
    description="Main API for CUFE extraction automation system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = get_logger(__name__)

# Service URLs
MAILBOX_SERVICE_URL = os.getenv("MAILBOX_SERVICE_URL", "http://localhost:8001")
FILE_PROCESSING_SERVICE_URL = os.getenv("FILE_PROCESSING_SERVICE_URL", "http://localhost:8002")
EXTRACTION_SERVICE_URL = os.getenv("EXTRACTION_SERVICE_URL", "http://localhost:8003")

def _convert_unit_to_enum(unit_str: str) -> Optional[MeasurementUnit]:
    """Convert string unit of measure to MeasurementUnit enum"""
    if not unit_str:
        return None

    unit_str = unit_str.lower().strip()

    # Common unit mappings
    unit_mappings = {
        'kg': MeasurementUnit.KILOGRAM,
        'kilogram': MeasurementUnit.KILOGRAM,
        'g': MeasurementUnit.GRAM,
        'gram': MeasurementUnit.GRAM,
        'l': MeasurementUnit.LITER,
        'liter': MeasurementUnit.LITER,
        'litre': MeasurementUnit.LITER,
        'ml': MeasurementUnit.MILLILITER,
        'milliliter': MeasurementUnit.MILLILITER,
        'm': MeasurementUnit.METER,
        'meter': MeasurementUnit.METER,
        'cm': MeasurementUnit.CENTIMETER,
        'centimeter': MeasurementUnit.CENTIMETER,
        'unit': MeasurementUnit.UNIT,
        'units': MeasurementUnit.UNIT,
        'pcs': MeasurementUnit.PIECE,
        'piece': MeasurementUnit.PIECE,
        'pieces': MeasurementUnit.PIECE,
        'box': MeasurementUnit.BOX,
        'bottle': MeasurementUnit.BOTTLE,
        'hr': MeasurementUnit.HOUR,
        'hour': MeasurementUnit.HOUR,
        'hours': MeasurementUnit.HOUR,
        'day': MeasurementUnit.DAY,
        'days': MeasurementUnit.DAY,
        '%': MeasurementUnit.PERCENTAGE,
        'percent': MeasurementUnit.PERCENTAGE,
        'percentage': MeasurementUnit.PERCENTAGE,
    }

    return unit_mappings.get(unit_str, MeasurementUnit.UNKNOWN)

def _convert_cufe_record_to_response(cufe_record: CUFERecord, db: Session) -> CUFEResponse:
    """
    Convert a CUFERecord database model to a CUFEResponse with all comprehensive invoice data
    """
    # Convert line items
    line_items = []
    for line_item in cufe_record.invoice_line_items:
        # Convert unit of measure to enum
        unit_of_measure = _convert_unit_to_enum(line_item.unit_of_measure)

        line_items.append(InvoiceLineItemData(
            line_number=line_item.line_number,
            item_name=line_item.item_name,
            item_description=line_item.item_description,
            item_code=line_item.item_code,
            invoiced_quantity=line_item.invoiced_quantity,
            unit_of_measure=unit_of_measure,
            unit_price=line_item.unit_price,
            subtotal_without_vat=getattr(line_item, 'subtotal_without_vat', line_item.line_extension_amount),
            line_extension_amount=line_item.line_extension_amount,
            line_tax_amount=line_item.line_tax_amount,
            line_tax_inclusive_amount=line_item.line_tax_inclusive_amount,
            allowance_charge_amount=line_item.allowance_charge_amount,
            free_of_charge_indicator=line_item.free_of_charge_indicator,
            vat_rate=getattr(line_item, 'vat_rate', None),
            vat_amount_per_unit=getattr(line_item, 'vat_amount_per_unit', None),
            tax_category_code=getattr(line_item, 'tax_category_code', None),
            tax_exemption_reason=getattr(line_item, 'tax_exemption_reason', None)
        ))

    # Convert allowance charges
    allowance_charges = []
    for ac in cufe_record.allowance_charges:
        allowance_charges.append(InvoiceAllowanceChargeData(
            charge_indicator=ac.charge_indicator,
            allowance_charge_reason_code=ac.allowance_charge_reason_code,
            allowance_charge_reason=ac.allowance_charge_reason,
            multiplier_factor_numeric=ac.multiplier_factor_numeric,
            amount=ac.amount,
            base_amount=ac.base_amount,
            tax_category=ac.tax_category,
            tax_amount=ac.tax_amount
        ))

    # Convert payment terms
    payment_terms = []
    for pt in cufe_record.payment_terms:
        payment_terms.append(InvoicePaymentTermData(
            payment_means_code=pt.payment_means_code,
            payment_due_date=pt.payment_due_date,
            payment_terms_note=pt.payment_terms_note,
            settlement_period_measure=pt.settlement_period_measure,
            settlement_period_unit=pt.settlement_period_unit,
            settlement_discount_percent=pt.settlement_discount_percent,
            penalty_surcharge_percent=pt.penalty_surcharge_percent,
            amount=pt.amount
        ))

    return CUFEResponse(
        cufe_value=cufe_record.cufe_value,
        email_id=cufe_record.email_record.email_id,
        reception_date=cufe_record.email_record.reception_date,
        xml_file_path=cufe_record.xml_file_record.file_path if cufe_record.xml_file_record else "",
        pdf_file_path=cufe_record.pdf_file_record.file_path if cufe_record.pdf_file_record else None,
        processed_date=cufe_record.extraction_date,

        # Basic invoice information
        issuer_name=cufe_record.issuer_name,
        document_number=cufe_record.document_number,
        issue_date=cufe_record.issue_date,
        total_amount=cufe_record.total_amount,

        # Enhanced tax and monetary details
        tax_exclusive_amount=cufe_record.tax_exclusive_amount,
        tax_inclusive_amount=cufe_record.tax_inclusive_amount,
        allowance_total_amount=cufe_record.allowance_total_amount,
        charge_total_amount=cufe_record.charge_total_amount,
        prepaid_amount=cufe_record.prepaid_amount,
        payable_amount=cufe_record.payable_amount,

        # Tax breakdown details
        total_tax_amount=cufe_record.total_tax_amount,
        iva_amount=cufe_record.iva_amount,
        rete_fuente_amount=cufe_record.rete_fuente_amount,
        rete_iva_amount=cufe_record.rete_iva_amount,
        rete_ica_amount=cufe_record.rete_ica_amount,

        # Additional invoice details
        due_date=cufe_record.due_date,
        currency_code=cufe_record.currency_code,
        invoice_type_code=cufe_record.invoice_type_code,
        accounting_cost=cufe_record.accounting_cost,

        # Related data collections
        line_items=line_items if line_items else None,
        allowance_charges=allowance_charges if allowance_charges else None,
        payment_terms=payment_terms if payment_terms else None
    )

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    """Create database tables on startup"""
    try:
        create_tables()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "api-service"}

# Authentication endpoints
@app.post("/auth/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """
    Authenticate user and return JWT token
    """
    logger.info(f"Login attempt for username: {login_data.username}")
    user = authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token = create_user_token(
        username=user.username,
        user_id=user.id,
        client_id=user.client_id
    )

    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user=user,
        client=user.client
    )

@app.get("/auth/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return {
        "user": current_user,
        "client": current_user.client
    }

@app.post("/auth/create-test-user")
async def create_test_user(db: Session = Depends(get_db)):
    """Create test user for development - REMOVE IN PRODUCTION"""
    try:
        # Check if test client exists
        test_client = db.query(Client).filter(Client.client_id == "TEST_CLIENT").first()
        if not test_client:
            test_client = Client(
                client_id="TEST_CLIENT",
                company_name="Test Company",
                is_active=True
            )
            db.add(test_client)
            db.commit()
            db.refresh(test_client)

        # Check if test user exists
        test_user = db.query(User).filter(User.username == "testuser").first()
        if not test_user:
            test_user = User(
                username="testuser",
                email="<EMAIL>",
                hashed_password=hash_password("password123"),
                full_name="Test User",
                client_id=test_client.id,
                is_active=True,
                is_admin=False
            )
            db.add(test_user)
            db.commit()

        return {"message": "Test user created", "username": "testuser", "password": "password123"}

    except Exception as e:
        logger.error(f"Error creating test user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create test user: {str(e)}")

@app.get("/auth/debug")
async def debug_auth(db: Session = Depends(get_db)):
    """Debug endpoint to check users and clients"""
    try:
        users = db.query(User).all()
        clients = db.query(Client).all()

        return {
            "users_count": len(users),
            "clients_count": len(clients),
            "users": [{"username": u.username, "email": u.email, "client_id": u.client_id} for u in users],
            "clients": [{"client_id": c.client_id, "company_name": c.company_name} for c in clients]
        }
    except Exception as e:
        return {"error": str(e)}

# Main API endpoints
@app.post("/process-emails")
async def process_emails(
    request: ProcessEmailsRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Trigger the complete email processing pipeline
    """
    try:
        logger.info(f"Processing emails requested by user: {current_user.username}")

        # Add to background tasks for async processing
        background_tasks.add_task(process_emails_pipeline, request, db, current_user.id, current_user.client_id)

        return {
            "message": "Email processing started",
            "status": "processing",
            "user": current_user.username
        }

    except Exception as e:
        logger.error(f"Error starting email processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/process-emails-sync")
async def process_emails_sync(
    request: ProcessEmailsRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Process emails synchronously (for frontend testing)
    """
    try:
        logger.info(f"Sync processing emails requested by user: {current_user.username}")

        # Call mailbox service directly
        async with httpx.AsyncClient() as client:
            # Convert to mailbox service request format
            mailbox_request = EmailProcessRequest(
                email_host=request.email_host,
                email_port=request.email_port,
                email_username=request.email_username,
                email_password=request.email_password,
                use_ssl=request.use_ssl,
                folder=request.folder,
                date_filter=request.date_filter,
                start_date=request.start_date,
                end_date=request.end_date,
                client_id=current_user.client_id
            )

            # Call mailbox service
            response = await client.post(
                f"{MAILBOX_SERVICE_URL}/process-emails",
                json=mailbox_request.dict(),
                timeout=300.0  # 5 minutes timeout
            )

            if response.status_code == 200:
                result = response.json()

                # Process the complete pipeline for sync response
                downloaded_files = result.get('downloaded_files', [])
                all_xml_files = []
                cufe_results = []

                # Process each downloaded ZIP file
                for downloaded_file in downloaded_files:
                    try:
                        # Process ZIP file
                        file_process_response = await client.post(
                            f"{FILE_PROCESSING_SERVICE_URL}/process-zip",
                            json={
                                "file_path": downloaded_file['file_path'],
                                "email_id": downloaded_file.get('email_id'),
                                "preserve_structure": True
                            },
                            timeout=120.0
                        )

                        if file_process_response.status_code == 200:
                            file_result = file_process_response.json()
                            xml_files = file_result.get('xml_files', [])
                            all_xml_files.extend(xml_files)

                            # Extract CUFE from each XML file
                            for xml_file in xml_files:
                                try:
                                    extraction_response = await client.post(
                                        f"{EXTRACTION_SERVICE_URL}/extract-cufe",
                                        json={
                                            "xml_file_path": xml_file['file_path'],
                                            "email_id": xml_file.get('email_id'),
                                            "extract_additional_data": True,
                                            "user_id": current_user.id,
                                            "client_id": current_user.client_id
                                        },
                                        timeout=60.0
                                    )

                                    if extraction_response.status_code == 200:
                                        extraction_result = extraction_response.json()
                                        if extraction_result.get('success') and extraction_result.get('cufe_value'):
                                            cufe_results.append({
                                                "cufe_value": extraction_result['cufe_value'],
                                                "xml_file": xml_file['filename'],
                                                "issuer_name": extraction_result.get('cufe_data', {}).get('issuer_name'),
                                                "document_number": extraction_result.get('cufe_data', {}).get('document_number'),
                                                "issue_date": extraction_result.get('cufe_data', {}).get('issue_date'),
                                                "total_amount": extraction_result.get('cufe_data', {}).get('total_amount'),
                                                "extraction_date": extraction_result.get('extraction_date')
                                            })
                                except Exception as e:
                                    logger.error(f"Error extracting CUFE from {xml_file['filename']}: {str(e)}")

                    except Exception as e:
                        logger.error(f"Error processing file {downloaded_file['filename']}: {str(e)}")

                # Enhanced response with CUFE information
                enhanced_result = result.copy()
                enhanced_result['extracted_xml_files'] = all_xml_files
                enhanced_result['cufe_extractions'] = cufe_results
                enhanced_result['cufe_count'] = len(cufe_results)

                return {
                    "message": "Email processing completed",
                    "status": "completed",
                    "result": enhanced_result,
                    "user": current_user.username
                }
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Mailbox service error: {response.text}"
                )

    except httpx.RequestError as e:
        logger.error(f"Error calling mailbox service: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Mailbox service unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error in sync email processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/cufe/{cufe_id}", response_model=CUFEResponse)
async def get_cufe(
    cufe_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get CUFE information by ID (filtered by client)
    """
    try:
        # Query CUFE record filtered by client
        cufe_record = db.query(CUFERecord).join(EmailRecord).filter(
            CUFERecord.cufe_value == cufe_id,
            EmailRecord.client_id == current_user.client_id
        ).first()

        if not cufe_record:
            raise HTTPException(status_code=404, detail="CUFE not found")

        return _convert_cufe_record_to_response(cufe_record, db)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CUFE {cufe_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE retrieval failed: {str(e)}")

@app.get("/cufe/", response_model=CUFEListResponse)
async def list_cufe_records(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List CUFE records for the authenticated user's client
    """
    try:
        # Query CUFE records filtered by client
        cufe_records = db.query(CUFERecord).join(EmailRecord).filter(
            EmailRecord.client_id == current_user.client_id
        ).offset(skip).limit(limit).all()

        total = db.query(CUFERecord).join(EmailRecord).filter(
            EmailRecord.client_id == current_user.client_id
        ).count()

        # Convert to response format
        records = []
        for cufe in cufe_records:
            records.append(_convert_cufe_record_to_response(cufe, db))

        return CUFEListResponse(
            records=records,
            total=total,
            skip=skip,
            limit=limit
        )

    except Exception as e:
        logger.error(f"Error listing CUFE records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE listing failed: {str(e)}")

@app.delete("/cufe/{cufe_id}")
async def delete_cufe_record(
    cufe_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a CUFE record (filtered by client)
    """
    try:
        # Query CUFE record filtered by client
        cufe_record = db.query(CUFERecord).join(EmailRecord).filter(
            CUFERecord.cufe_value == cufe_id,
            EmailRecord.client_id == current_user.client_id
        ).first()

        if not cufe_record:
            raise HTTPException(status_code=404, detail="CUFE not found")

        # Store info for response
        deleted_cufe = cufe_record.cufe_value
        deleted_issuer = cufe_record.issuer_name

        # Delete the record (cascade will handle related records)
        db.delete(cufe_record)
        db.commit()

        logger.info(f"CUFE record deleted by user {current_user.username}: {deleted_cufe}")

        return {
            "success": True,
            "message": f"Invoice record deleted successfully",
            "deleted_cufe": deleted_cufe,
            "deleted_issuer": deleted_issuer
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting CUFE {cufe_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"CUFE deletion failed: {str(e)}")

@app.get("/download/{file_type}/{cufe_id}")
async def download_file(
    file_type: str,
    cufe_id: str,
    db = Depends(get_db)
):
    """
    Download XML or PDF file associated with a CUFE
    """
    try:
        if file_type not in ["xml", "pdf"]:
            raise HTTPException(status_code=400, detail="File type must be 'xml' or 'pdf'")
        
        # TODO: Get file path from database and return file
        # cufe_record = db.query(CUFERecord).filter(CUFERecord.cufe_value == cufe_id).first()
        
        # Placeholder - return error for now
        raise HTTPException(status_code=404, detail="File not found")
        
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File download failed: {str(e)}")

# Background task function
async def process_emails_pipeline(request: ProcessEmailsRequest, db: Session, user_id: int, client_id: int):
    """
    Background task to process emails through the pipeline
    """
    try:
        logger.info("Starting email processing pipeline")

        # Step 1: Call mailbox service
        async with httpx.AsyncClient() as client:
            mailbox_request = EmailProcessRequest(
                email_host=request.email_host,
                email_port=request.email_port,
                email_username=request.email_username,
                email_password=request.email_password,
                use_ssl=request.use_ssl,
                folder=request.folder,
                date_filter=request.date_filter,
                start_date=request.start_date,
                end_date=request.end_date,
                client_id=client_id
            )

            try:
                response = await client.post(
                    f"{MAILBOX_SERVICE_URL}/process-emails",
                    json=mailbox_request.dict(),
                    timeout=300.0
                )

                if response.status_code == 200:
                    mailbox_result = response.json()
                    logger.info(f"Mailbox processing completed: {mailbox_result}")

                    # Step 2: Call file processing service for each downloaded file
                    downloaded_files = mailbox_result.get('downloaded_files', [])
                    all_xml_files = []

                    for downloaded_file in downloaded_files:
                        try:
                            # Process ZIP file
                            file_process_response = await client.post(
                                f"{FILE_PROCESSING_SERVICE_URL}/process-zip",
                                json={
                                    "file_path": downloaded_file['file_path'],
                                    "email_id": downloaded_file.get('email_id'),
                                    "preserve_structure": True
                                },
                                timeout=120.0
                            )

                            if file_process_response.status_code == 200:
                                file_result = file_process_response.json()
                                xml_files = file_result.get('xml_files', [])
                                all_xml_files.extend(xml_files)
                                logger.info(f"Processed ZIP file {downloaded_file['filename']}: {len(xml_files)} XML files extracted")
                            else:
                                logger.error(f"File processing failed for {downloaded_file['filename']}: {file_process_response.text}")

                        except Exception as e:
                            logger.error(f"Error processing file {downloaded_file['filename']}: {str(e)}")

                    # Step 3: Call extraction service for each XML file
                    cufe_results = []

                    for xml_file in all_xml_files:
                        try:
                            # Extract CUFE from XML file
                            extraction_response = await client.post(
                                f"{EXTRACTION_SERVICE_URL}/extract-cufe",
                                json={
                                    "xml_file_path": xml_file['file_path'],
                                    "email_id": xml_file.get('email_id'),
                                    "extract_additional_data": True,
                                    "user_id": user_id,
                                    "client_id": client_id
                                },
                                timeout=60.0
                            )

                            if extraction_response.status_code == 200:
                                extraction_result = extraction_response.json()
                                if extraction_result.get('success') and extraction_result.get('cufe_value'):
                                    cufe_results.append(extraction_result)
                                    logger.info(f"Extracted CUFE from {xml_file['filename']}: {extraction_result['cufe_value']}")
                                else:
                                    logger.warning(f"No CUFE found in {xml_file['filename']}")
                            else:
                                logger.error(f"CUFE extraction failed for {xml_file['filename']}: {extraction_response.text}")

                        except Exception as e:
                            logger.error(f"Error extracting CUFE from {xml_file['filename']}: {str(e)}")

                    # Step 4: Results are already stored in database by the extraction service
                    logger.info(f"Pipeline completed: {len(cufe_results)} CUFE values extracted")

                else:
                    logger.error(f"Mailbox service error: {response.status_code} - {response.text}")

            except httpx.RequestError as e:
                logger.error(f"Failed to call mailbox service: {str(e)}")

        logger.info("Email processing pipeline completed")

    except Exception as e:
        logger.error(f"Error in email processing pipeline: {str(e)}")

# File download endpoints
@app.get("/download/zip/{file_path:path}")
async def download_zip_file(file_path: str):
    """
    Download a ZIP file by its file path
    """
    try:
        # Decode the URL-encoded file path
        decoded_path = unquote(file_path)

        # Validate file exists and is a ZIP file
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail=f"File not found: {decoded_path}")

        if not decoded_path.lower().endswith('.zip'):
            raise HTTPException(status_code=400, detail="Only ZIP files can be downloaded")

        # Get filename for download
        filename = os.path.basename(decoded_path)

        logger.info(f"Serving download for file: {decoded_path}")

        return FileResponse(
            path=decoded_path,
            filename=filename,
            media_type='application/zip'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving file download: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

def _create_excel_export(cufe_records: List[CUFERecord], db: Session) -> openpyxl.Workbook:
    """
    Create Excel file with comprehensive invoice data
    """
    # Create workbook and worksheets
    wb = openpyxl.Workbook()

    # Remove default sheet
    wb.remove(wb.active)

    # Create sheets
    summary_sheet = wb.create_sheet("Resumen de Facturas")
    details_sheet = wb.create_sheet("Detalle de Artículos")
    companies_sheet = wb.create_sheet("Información de Empresas")

    # Define styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # === SUMMARY SHEET ===
    summary_headers = [
        "CUFE", "Número de Documento", "Fecha de Emisión", "Emisor",
        "Subtotal sin IVA", "IVA Total", "Total con IVA", "Moneda",
        "Fecha de Procesamiento", "Estado"
    ]

    # Add headers to summary sheet
    for col, header in enumerate(summary_headers, 1):
        cell = summary_sheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Add summary data
    for row, record in enumerate(cufe_records, 2):
        summary_sheet.cell(row=row, column=1, value=record.cufe_value)
        summary_sheet.cell(row=row, column=2, value=record.document_number or "N/A")
        summary_sheet.cell(row=row, column=3, value=record.issue_date.strftime("%Y-%m-%d") if record.issue_date else "N/A")
        summary_sheet.cell(row=row, column=4, value=record.issuer_name or "N/A")
        summary_sheet.cell(row=row, column=5, value=float(record.tax_exclusive_amount) if record.tax_exclusive_amount else 0)
        summary_sheet.cell(row=row, column=6, value=float(record.iva_amount) if record.iva_amount else 0)
        summary_sheet.cell(row=row, column=7, value=float(record.tax_inclusive_amount) if record.tax_inclusive_amount else 0)
        summary_sheet.cell(row=row, column=8, value=record.currency_code or "COP")
        summary_sheet.cell(row=row, column=9, value=record.extraction_date.strftime("%Y-%m-%d %H:%M") if record.extraction_date else "N/A")
        summary_sheet.cell(row=row, column=10, value="Procesado")

    # Auto-adjust column widths for summary
    for col in range(1, len(summary_headers) + 1):
        summary_sheet.column_dimensions[get_column_letter(col)].width = 15

    # === DETAILS SHEET ===
    details_headers = [
        "CUFE", "Línea", "Nombre del Artículo", "Descripción", "Código",
        "Cantidad", "Unidad de Medida", "Precio Unitario", "Subtotal sin IVA",
        "Tasa de IVA (%)", "IVA por Artículo", "Total con IVA", "Categoría Fiscal"
    ]

    # Add headers to details sheet
    for col, header in enumerate(details_headers, 1):
        cell = details_sheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Add line items data
    detail_row = 2
    for record in cufe_records:
        line_items = db.query(InvoiceLineItem).filter(InvoiceLineItem.cufe_record_id == record.id).all()

        if not line_items:
            # Add a row even if no line items
            details_sheet.cell(row=detail_row, column=1, value=record.cufe_value)
            details_sheet.cell(row=detail_row, column=2, value="N/A")
            details_sheet.cell(row=detail_row, column=3, value="Sin detalle de artículos")
            detail_row += 1
        else:
            for item in line_items:
                details_sheet.cell(row=detail_row, column=1, value=record.cufe_value)
                details_sheet.cell(row=detail_row, column=2, value=item.line_number or "N/A")
                details_sheet.cell(row=detail_row, column=3, value=item.item_name or "N/A")
                details_sheet.cell(row=detail_row, column=4, value=item.item_description or "N/A")
                details_sheet.cell(row=detail_row, column=5, value=item.item_code or "N/A")
                details_sheet.cell(row=detail_row, column=6, value=item.invoiced_quantity or "N/A")
                details_sheet.cell(row=detail_row, column=7, value=item.unit_of_measure or "N/A")
                details_sheet.cell(row=detail_row, column=8, value=float(item.unit_price) if item.unit_price else 0)
                details_sheet.cell(row=detail_row, column=9, value=float(item.line_extension_amount) if item.line_extension_amount else 0)
                details_sheet.cell(row=detail_row, column=10, value="19.00")  # Default VAT rate for Colombia
                details_sheet.cell(row=detail_row, column=11, value=float(item.line_tax_amount) if item.line_tax_amount else 0)
                details_sheet.cell(row=detail_row, column=12, value=float(item.line_tax_inclusive_amount) if item.line_tax_inclusive_amount else 0)
                details_sheet.cell(row=detail_row, column=13, value="IVA General")
                detail_row += 1

    # Auto-adjust column widths for details
    for col in range(1, len(details_headers) + 1):
        details_sheet.column_dimensions[get_column_letter(col)].width = 18

    # === COMPANIES SHEET ===
    companies_headers = [
        "CUFE", "Tipo", "Nombre de la Empresa", "NIT/ID", "Dirección",
        "Ciudad", "Teléfono", "Email", "Sector Industrial"
    ]

    # Add headers to companies sheet
    for col, header in enumerate(companies_headers, 1):
        cell = companies_sheet.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Add company data (for now, just basic issuer info from CUFE records)
    for row, record in enumerate(cufe_records, 2):
        companies_sheet.cell(row=row, column=1, value=record.cufe_value)
        companies_sheet.cell(row=row, column=2, value="Emisor")
        companies_sheet.cell(row=row, column=3, value=record.issuer_name or "N/A")
        companies_sheet.cell(row=row, column=4, value="N/A")  # NIT not extracted yet
        companies_sheet.cell(row=row, column=5, value="N/A")  # Address not extracted yet
        companies_sheet.cell(row=row, column=6, value="N/A")  # City not extracted yet
        companies_sheet.cell(row=row, column=7, value="N/A")  # Phone not extracted yet
        companies_sheet.cell(row=row, column=8, value="N/A")  # Email not extracted yet
        companies_sheet.cell(row=row, column=9, value="N/A")  # Industry sector not extracted yet

    # Auto-adjust column widths for companies
    for col in range(1, len(companies_headers) + 1):
        companies_sheet.column_dimensions[get_column_letter(col)].width = 20

    return wb

@app.get("/export-excel")
async def export_invoices_to_excel(
    skip: int = 0,
    limit: int = 1000,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export invoice records to Excel format
    """
    try:
        logger.info(f"Excel export requested by user: {current_user.username}")

        # Get CUFE records for the user's client
        cufe_records = db.query(CUFERecord).join(EmailRecord).filter(
            EmailRecord.client_id == current_user.client_id
        ).offset(skip).limit(limit).all()

        if not cufe_records:
            raise HTTPException(status_code=404, detail="No invoice records found")

        logger.info(f"Exporting {len(cufe_records)} invoice records to Excel")

        # Create Excel file
        wb = _create_excel_export(cufe_records, db)

        # Save to BytesIO
        excel_buffer = io.BytesIO()
        wb.save(excel_buffer)
        excel_buffer.seek(0)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facturas_export_{timestamp}.xlsx"

        logger.info(f"Excel export completed: {filename}")

        return StreamingResponse(
            io.BytesIO(excel_buffer.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating Excel export: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Excel export failed: {str(e)}")

# Admin endpoints
@app.get("/admin/users", response_model=UserListResponse)
async def list_users(
    page: int = 1,
    per_page: int = 20,
    search: Optional[str] = None,
    client_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    List all users with pagination and filtering (Admin only)
    """
    try:
        # Build query
        query = db.query(User).join(Client)

        # Apply filters
        if search:
            query = query.filter(
                (User.username.ilike(f"%{search}%")) |
                (User.email.ilike(f"%{search}%")) |
                (User.full_name.ilike(f"%{search}%"))
            )

        if client_id:
            query = query.filter(User.client_id == client_id)

        if is_active is not None:
            query = query.filter(User.is_active == is_active)

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * per_page
        users = query.offset(offset).limit(per_page).all()

        # Convert to response format
        user_responses = []
        for user in users:
            user_response = AdminUserResponse(
                id=user.id,
                username=user.username,
                email=user.email,
                full_name=user.full_name,
                client_id=user.client_id,
                client_name=user.client.company_name if user.client else None,
                is_admin=user.is_admin,
                is_active=user.is_active,
                created_date=user.created_date,
                last_login=user.last_login
            )
            user_responses.append(user_response)

        return UserListResponse(
            users=user_responses,
            total=total,
            page=page,
            per_page=per_page,
            has_next=offset + per_page < total,
            has_prev=page > 1
        )

    except Exception as e:
        logger.error(f"Error listing users: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list users: {str(e)}")

@app.post("/admin/users", response_model=AdminResponse)
async def create_user(
    user_data: AdminUserCreate,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Create a new user (Admin only)
    """
    try:
        # Check if username or email already exists
        existing_user = db.query(User).filter(
            (User.username == user_data.username) | (User.email == user_data.email)
        ).first()

        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="Username or email already exists"
            )

        # Check if client exists
        client = db.query(Client).filter(Client.id == user_data.client_id).first()
        if not client:
            raise HTTPException(
                status_code=400,
                detail="Client not found"
            )

        # Create new user
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hash_password(user_data.password),
            full_name=user_data.full_name,
            client_id=user_data.client_id,
            is_admin=user_data.is_admin,
            is_active=user_data.is_active
        )

        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        logger.info(f"Admin {current_admin.username} created user {new_user.username}")

        return AdminResponse(
            success=True,
            message=f"User {new_user.username} created successfully",
            data={"user_id": new_user.id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create user: {str(e)}")

@app.put("/admin/users/{user_id}/suspend", response_model=AdminResponse)
async def suspend_user(
    user_id: int,
    suspend_data: UserSuspendRequest,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Suspend a user account (Admin only)
    """
    try:
        # Prevent admin from suspending themselves
        if user_id == current_admin.id:
            raise HTTPException(
                status_code=400,
                detail="Cannot suspend your own account"
            )

        # Find user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Suspend user
        user.is_active = False
        db.commit()

        logger.info(f"Admin {current_admin.username} suspended user {user.username}. Reason: {suspend_data.reason}")

        return AdminResponse(
            success=True,
            message=f"User {user.username} suspended successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error suspending user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to suspend user: {str(e)}")

@app.put("/admin/users/{user_id}/activate", response_model=AdminResponse)
async def activate_user(
    user_id: int,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Activate a user account (Admin only)
    """
    try:
        # Find user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Activate user
        user.is_active = True
        db.commit()

        logger.info(f"Admin {current_admin.username} activated user {user.username}")

        return AdminResponse(
            success=True,
            message=f"User {user.username} activated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to activate user: {str(e)}")

@app.put("/admin/users/{user_id}", response_model=AdminResponse)
async def update_user(
    user_id: int,
    user_data: AdminUserUpdate,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update user information (Admin only)
    """
    try:
        # Find user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Update fields if provided
        if user_data.username is not None:
            # Check if username already exists
            existing = db.query(User).filter(
                User.username == user_data.username,
                User.id != user_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Username already exists")
            user.username = user_data.username

        if user_data.email is not None:
            # Check if email already exists
            existing = db.query(User).filter(
                User.email == user_data.email,
                User.id != user_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Email already exists")
            user.email = user_data.email

        if user_data.full_name is not None:
            user.full_name = user_data.full_name

        if user_data.client_id is not None:
            # Check if client exists
            client = db.query(Client).filter(Client.id == user_data.client_id).first()
            if not client:
                raise HTTPException(status_code=400, detail="Client not found")
            user.client_id = user_data.client_id

        if user_data.is_admin is not None:
            user.is_admin = user_data.is_admin

        if user_data.is_active is not None:
            user.is_active = user_data.is_active

        db.commit()

        logger.info(f"Admin {current_admin.username} updated user {user.username}")

        return AdminResponse(
            success=True,
            message=f"User {user.username} updated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update user: {str(e)}")

@app.get("/admin/costs/llm")
async def get_llm_costs(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    user_id: Optional[int] = None,
    client_id: Optional[int] = None,
    provider: Optional[str] = None,
    page: int = 1,
    per_page: int = 50,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get LLM usage costs with filtering (Admin only)
    """
    try:
        # Build query
        query = db.query(LLMUsageRecord).join(User).join(Client)

        # Apply filters
        if start_date:
            query = query.filter(LLMUsageRecord.created_at >= start_date)
        if end_date:
            query = query.filter(LLMUsageRecord.created_at <= end_date)
        if user_id:
            query = query.filter(LLMUsageRecord.user_id == user_id)
        if client_id:
            query = query.filter(LLMUsageRecord.client_id == client_id)
        if provider:
            query = query.filter(LLMUsageRecord.provider == provider)

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * per_page
        records = query.order_by(LLMUsageRecord.created_at.desc()).offset(offset).limit(per_page).all()

        # Convert to response format
        usage_responses = []
        for record in records:
            usage_response = LLMUsageResponse(
                id=record.id,
                user_id=record.user_id,
                username=record.user.username if record.user else None,
                client_id=record.client_id,
                client_name=record.client.company_name if record.client else None,
                provider=record.provider,
                model_name=record.model_name,
                input_tokens=record.input_tokens,
                output_tokens=record.output_tokens,
                total_tokens=record.total_tokens,
                input_cost=record.input_cost,
                output_cost=record.output_cost,
                total_cost=record.total_cost,
                request_type=record.request_type,
                processing_time_ms=record.processing_time_ms,
                success=record.success,
                error_message=record.error_message,
                created_at=record.created_at
            )
            usage_responses.append(usage_response)

        return {
            "records": usage_responses,
            "total": total,
            "page": page,
            "per_page": per_page,
            "has_next": offset + per_page < total,
            "has_prev": page > 1
        }

    except Exception as e:
        logger.error(f"Error getting LLM costs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get LLM costs: {str(e)}")

@app.get("/admin/costs/deployment")
async def get_deployment_costs(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    service_name: Optional[str] = None,
    cost_type: Optional[str] = None,
    page: int = 1,
    per_page: int = 50,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get deployment costs with filtering (Admin only)
    """
    try:
        # Build query
        query = db.query(DeploymentCostRecord)

        # Apply filters
        if start_date:
            query = query.filter(DeploymentCostRecord.period_start >= start_date)
        if end_date:
            query = query.filter(DeploymentCostRecord.period_end <= end_date)
        if service_name:
            query = query.filter(DeploymentCostRecord.service_name == service_name)
        if cost_type:
            query = query.filter(DeploymentCostRecord.cost_type == cost_type)

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * per_page
        records = query.order_by(DeploymentCostRecord.created_at.desc()).offset(offset).limit(per_page).all()

        # Convert to response format
        cost_responses = []
        for record in records:
            cost_response = DeploymentCostResponse(
                id=record.id,
                service_name=record.service_name,
                cost_type=record.cost_type,
                cost_amount=record.cost_amount,
                currency=record.currency,
                period_start=record.period_start,
                period_end=record.period_end,
                billing_period=record.billing_period,
                resource_usage=record.resource_usage,
                resource_units=record.resource_units,
                provider=record.provider,
                region=record.region,
                notes=record.notes,
                created_at=record.created_at,
                updated_at=record.updated_at
            )
            cost_responses.append(cost_response)

        return {
            "records": cost_responses,
            "total": total,
            "page": page,
            "per_page": per_page,
            "has_next": offset + per_page < total,
            "has_prev": page > 1
        }

    except Exception as e:
        logger.error(f"Error getting deployment costs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get deployment costs: {str(e)}")

@app.get("/admin/dashboard/stats", response_model=AdminDashboardStats)
async def get_admin_dashboard_stats(
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get admin dashboard statistics (Admin only)
    """
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func, Float

        today = datetime.now().date()
        month_start = datetime.now().replace(day=1).date()

        # User statistics
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        suspended_users = db.query(User).filter(User.is_active == False).count()

        # Client statistics
        total_clients = db.query(Client).count()
        active_clients = db.query(Client).filter(Client.is_active == True).count()

        # LLM usage statistics
        llm_requests_today = db.query(LLMUsageRecord).filter(
            func.date(LLMUsageRecord.created_at) == today
        ).count()

        llm_requests_this_month = db.query(LLMUsageRecord).filter(
            func.date(LLMUsageRecord.created_at) >= month_start
        ).count()

        # LLM cost statistics
        llm_cost_today_result = db.query(
            func.sum(func.cast(LLMUsageRecord.total_cost, Float))
        ).filter(
            func.date(LLMUsageRecord.created_at) == today
        ).scalar()
        llm_cost_today = str(llm_cost_today_result or 0.0)

        llm_cost_this_month_result = db.query(
            func.sum(func.cast(LLMUsageRecord.total_cost, Float))
        ).filter(
            func.date(LLMUsageRecord.created_at) >= month_start
        ).scalar()
        llm_cost_this_month = str(llm_cost_this_month_result or 0.0)

        # Invoice processing statistics
        total_invoices_processed = db.query(CUFERecord).count()
        invoices_processed_today = db.query(CUFERecord).filter(
            func.date(CUFERecord.extraction_date) == today
        ).count()
        invoices_processed_this_month = db.query(CUFERecord).filter(
            func.date(CUFERecord.extraction_date) >= month_start
        ).count()

        # Recent users (last 5)
        recent_users_query = db.query(User).join(Client).order_by(
            User.created_date.desc()
        ).limit(5).all()

        recent_users = []
        for user in recent_users_query:
            recent_users.append(AdminUserResponse(
                id=user.id,
                username=user.username,
                email=user.email,
                full_name=user.full_name,
                client_id=user.client_id,
                client_name=user.client.company_name if user.client else None,
                is_admin=user.is_admin,
                is_active=user.is_active,
                created_date=user.created_date,
                last_login=user.last_login
            ))

        # Recent LLM usage (last 5)
        recent_llm_usage_query = db.query(LLMUsageRecord).join(User).join(Client).order_by(
            LLMUsageRecord.created_at.desc()
        ).limit(5).all()

        recent_llm_usage = []
        for record in recent_llm_usage_query:
            recent_llm_usage.append(LLMUsageResponse(
                id=record.id,
                user_id=record.user_id,
                username=record.user.username if record.user else None,
                client_id=record.client_id,
                client_name=record.client.company_name if record.client else None,
                provider=record.provider,
                model_name=record.model_name,
                input_tokens=record.input_tokens,
                output_tokens=record.output_tokens,
                total_tokens=record.total_tokens,
                input_cost=record.input_cost,
                output_cost=record.output_cost,
                total_cost=record.total_cost,
                request_type=record.request_type,
                processing_time_ms=record.processing_time_ms,
                success=record.success,
                error_message=record.error_message,
                created_at=record.created_at
            ))

        return AdminDashboardStats(
            total_users=total_users,
            active_users=active_users,
            suspended_users=suspended_users,
            total_clients=total_clients,
            active_clients=active_clients,
            llm_requests_today=llm_requests_today,
            llm_requests_this_month=llm_requests_this_month,
            llm_cost_today=llm_cost_today,
            llm_cost_this_month=llm_cost_this_month,
            total_invoices_processed=total_invoices_processed,
            invoices_processed_today=invoices_processed_today,
            invoices_processed_this_month=invoices_processed_this_month,
            recent_users=recent_users,
            recent_llm_usage=recent_llm_usage
        )

    except Exception as e:
        logger.error(f"Error getting admin dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard stats: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
